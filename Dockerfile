# 1Panel Docker Setup for Hugging Face Spaces
FROM ubuntu:22.04

# 设置非交互模式和时区
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    sudo \
    systemd \
    docker.io \
    nginx \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# 创建用户 (Hugging Face Spaces 要求)
RUN useradd -m -u 1000 user && \
    usermod -aG sudo user && \
    echo "user ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 切换到 root 进行 1Panel 安装
USER root

# 下载并安装 1Panel
RUN curl -sSL https://resource.fit2cloud.com/1panel/package/quick_start.sh -o /tmp/quick_start.sh && \
    chmod +x /tmp/quick_start.sh

# 创建 1Panel 配置目录
RUN mkdir -p /opt/1panel

# 创建 supervisor 配置
RUN echo '[supervisord]' > /etc/supervisor/conf.d/1panel.conf && \
    echo 'nodaemon=true' >> /etc/supervisor/conf.d/1panel.conf && \
    echo '' >> /etc/supervisor/conf.d/1panel.conf && \
    echo '[program:1panel]' >> /etc/supervisor/conf.d/1panel.conf && \
    echo 'command=/usr/local/bin/1panel' >> /etc/supervisor/conf.d/1panel.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/1panel.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/1panel.conf && \
    echo 'user=root' >> /etc/supervisor/conf.d/1panel.conf && \
    echo 'stdout_logfile=/var/log/1panel.log' >> /etc/supervisor/conf.d/1panel.conf && \
    echo 'stderr_logfile=/var/log/1panel.error.log' >> /etc/supervisor/conf.d/1panel.conf

# 创建 nginx 反向代理配置 (将 1Panel 的端口映射到 7860)
RUN echo 'server {' > /etc/nginx/sites-available/1panel && \
    echo '    listen 7860;' >> /etc/nginx/sites-available/1panel && \
    echo '    server_name localhost;' >> /etc/nginx/sites-available/1panel && \
    echo '    location / {' >> /etc/nginx/sites-available/1panel && \
    echo '        proxy_pass http://127.0.0.1:10086;' >> /etc/nginx/sites-available/1panel && \
    echo '        proxy_set_header Host $host;' >> /etc/nginx/sites-available/1panel && \
    echo '        proxy_set_header X-Real-IP $remote_addr;' >> /etc/nginx/sites-available/1panel && \
    echo '        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;' >> /etc/nginx/sites-available/1panel && \
    echo '        proxy_set_header X-Forwarded-Proto $scheme;' >> /etc/nginx/sites-available/1panel && \
    echo '    }' >> /etc/nginx/sites-available/1panel && \
    echo '}' >> /etc/nginx/sites-available/1panel && \
    ln -s /etc/nginx/sites-available/1panel /etc/nginx/sites-enabled/ && \
    rm /etc/nginx/sites-enabled/default

# 创建启动脚本
RUN echo '#!/bin/bash' > /start.sh && \
    echo 'set -e' >> /start.sh && \
    echo '' >> /start.sh && \
    echo '# 安装 1Panel (如果还未安装)' >> /start.sh && \
    echo 'if [ ! -f /usr/local/bin/1panel ]; then' >> /start.sh && \
    echo '    echo "Installing 1Panel..."' >> /start.sh && \
    echo '    bash /tmp/quick_start.sh' >> /start.sh && \
    echo 'fi' >> /start.sh && \
    echo '' >> /start.sh && \
    echo '# 启动服务' >> /start.sh && \
    echo 'service nginx start' >> /start.sh && \
    echo 'supervisord -c /etc/supervisor/supervisord.conf' >> /start.sh && \
    chmod +x /start.sh

# 切换到用户目录
USER user
WORKDIR /home/<USER>

# 暴露端口 7860 (Hugging Face Spaces 要求)
EXPOSE 7860

# 切换回 root 启动服务
USER root

# 启动脚本
CMD ["/start.sh"]
