#!/usr/bin/env python3
"""
1Panel Web Interface Proxy for Hugging Face Spaces
"""

import subprocess
import time
import os
import signal
import sys
from threading import Thread
import requests
from flask import Flask, request, Response
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# 1Panel 默认端口
PANEL_PORT = 10086
PROXY_PORT = 7860

class PanelManager:
    def __init__(self):
        self.panel_process = None
        self.is_running = False
    
    def start_1panel(self):
        """启动 1Panel"""
        try:
            logger.info("Starting 1Panel...")
            
            # 检查是否已经安装
            if not os.path.exists('/usr/local/bin/1panel'):
                logger.info("Installing 1Panel...")
                install_cmd = "curl -sSL https://resource.fit2cloud.com/1panel/package/quick_start.sh | bash"
                subprocess.run(install_cmd, shell=True, check=True)
            
            # 启动 1Panel
            self.panel_process = subprocess.Popen(
                ['/usr/local/bin/1panel'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务启动
            self.wait_for_service()
            self.is_running = True
            logger.info("1Panel started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start 1Panel: {e}")
            self.is_running = False
    
    def wait_for_service(self, timeout=60):
        """等待服务启动"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f'http://localhost:{PANEL_PORT}', timeout=5)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(2)
        return False
    
    def stop(self):
        """停止 1Panel"""
        if self.panel_process:
            self.panel_process.terminate()
            self.panel_process.wait()
        self.is_running = False

# 全局 Panel 管理器
panel_manager = PanelManager()

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def proxy(path):
    """代理请求到 1Panel"""
    if not panel_manager.is_running:
        return "1Panel is starting, please wait...", 503
    
    try:
        # 构建目标 URL
        target_url = f'http://localhost:{PANEL_PORT}/{path}'
        
        # 转发请求
        if request.method == 'GET':
            resp = requests.get(target_url, params=request.args, timeout=30)
        elif request.method == 'POST':
            resp = requests.post(target_url, 
                               data=request.get_data(),
                               params=request.args,
                               headers=dict(request.headers),
                               timeout=30)
        else:
            resp = requests.request(request.method, target_url,
                                  data=request.get_data(),
                                  params=request.args,
                                  headers=dict(request.headers),
                                  timeout=30)
        
        # 返回响应
        return Response(resp.content, 
                       status=resp.status_code,
                       headers=dict(resp.headers))
    
    except Exception as e:
        logger.error(f"Proxy error: {e}")
        return f"Proxy error: {e}", 500

@app.route('/health')
def health():
    """健康检查"""
    return {
        "status": "running" if panel_manager.is_running else "starting",
        "panel_port": PANEL_PORT,
        "proxy_port": PROXY_PORT
    }

def signal_handler(sig, frame):
    """信号处理"""
    logger.info("Shutting down...")
    panel_manager.stop()
    sys.exit(0)

if __name__ == '__main__':
    # 注册信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 在后台线程启动 1Panel
    panel_thread = Thread(target=panel_manager.start_1panel)
    panel_thread.daemon = True
    panel_thread.start()
    
    # 启动 Flask 应用
    logger.info(f"Starting proxy server on port {PROXY_PORT}")
    app.run(host='0.0.0.0', port=PROXY_PORT, debug=False)
