#!/bin/bash

echo "=== 1Panel Installation for Hugging Face Spaces ==="
echo "Note: This is a demonstration setup with limited functionality"
echo "Some system-level features may not work in this containerized environment"
echo ""

# 创建一个简单的 Web 界面来显示信息
cat > /app/simple_panel.py << 'EOF'
from flask import Flask, render_template_string
import subprocess
import os

app = Flask(__name__)

TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>1Panel Demo - Hugging Face Spaces</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .status { padding: 15px; margin: 20px 0; border-radius: 5px; }
        .info { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .feature { margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 1Panel Demo</h1>
            <p>Modern Linux Server Management Panel</p>
        </div>
        
        <div class="status warning">
            <h3>⚠️ Important Notice</h3>
            <p>This is running in a Hugging Face Spaces container with limited privileges. 
            Full 1Panel functionality requires root access and systemd, which are not available in this environment.</p>
        </div>
        
        <div class="status info">
            <h3>📋 System Information</h3>
            <pre>{{ system_info }}</pre>
        </div>
        
        <div class="feature">
            <h3>🚀 What is 1Panel?</h3>
            <p>1Panel is a modern, web-based Linux server management panel that provides:</p>
            <ul>
                <li>Web-based server management interface</li>
                <li>Docker container management</li>
                <li>File management system</li>
                <li>System monitoring and logs</li>
                <li>Application installation and management</li>
                <li>Database management</li>
                <li>SSL certificate management</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🔧 Installation Instructions</h3>
            <p>To install 1Panel on your own server, run:</p>
            <pre>curl -sSL https://resource.fit2cloud.com/1panel/package/quick_start.sh | bash</pre>
        </div>
        
        <div class="feature">
            <h3>🌐 Links</h3>
            <a href="https://1panel.cn/" class="btn" target="_blank">Official Website</a>
            <a href="https://github.com/1Panel-dev/1Panel" class="btn" target="_blank">GitHub Repository</a>
            <a href="https://1panel.cn/docs/" class="btn" target="_blank">Documentation</a>
        </div>
        
        <div class="status info">
            <h3>💡 Demo Limitations</h3>
            <p>In this Hugging Face Spaces environment:</p>
            <ul>
                <li>No root privileges available</li>
                <li>No systemd services</li>
                <li>Limited system access</li>
                <li>No persistent storage</li>
                <li>Network restrictions apply</li>
            </ul>
            <p>For full functionality, install 1Panel on your own Linux server.</p>
        </div>
    </div>
</body>
</html>
'''

@app.route('/')
def home():
    # 获取系统信息
    try:
        system_info = subprocess.check_output(['uname', '-a'], text=True).strip()
        system_info += "\n" + subprocess.check_output(['whoami'], text=True).strip()
        system_info += "\nPython: " + subprocess.check_output(['python', '--version'], text=True).strip()
        system_info += "\nWorking Directory: " + os.getcwd()
        system_info += "\nEnvironment: Hugging Face Spaces"
    except:
        system_info = "System information unavailable"
    
    return render_template_string(TEMPLATE, system_info=system_info)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7860, debug=False)
EOF

# 启动简化的演示界面
echo "Starting 1Panel Demo Interface..."
python /app/simple_panel.py
